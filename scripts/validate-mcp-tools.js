#!/usr/bin/env node

/**
 * MCP Tools Validation Script
 * Validates all MCP tools are properly registered and functional
 */

import { spawn } from "child_process";
import { promises as fs } from "fs";

class MCPValidator {
  constructor() {
    this.results = {
      toolsFound: 0,
      categoriesFound: 0,
      validationPassed: false,
      errors: [],
      warnings: [],
    };
  }

  /**
   * Expected tool categories and minimum counts
   */
  getExpectedTools() {
    return {
      "Project Management": 12,
      "File Operations": 13,
      "Build & Test": 7,
      "Package Management": 15,
      "Simulator Control": 11,
      "Xcode Integration": 8,
      "Development Tools": 4,
    };
  }

  /**
   * Test server startup and tool registration
   */
  async validateServer() {
    console.log("🔍 Validating MCP Server and Tool Registration...\n");

    try {
      // Test 1: Check if server can start
      console.log("1️⃣  Testing server startup...");
      const startupTest = await this.testServerStartup();
      if (!startupTest) {
        this.results.errors.push("Server startup failed");
        return false;
      }
      console.log("✅ Server startup successful\n");

      // Test 2: Validate tool registration by checking source files
      console.log("2️⃣  Validating tool registration...");
      const toolValidation = await this.validateToolRegistration();
      if (!toolValidation) {
        this.results.errors.push("Tool registration validation failed");
        return false;
      }
      console.log("✅ Tool registration validation successful\n");

      // Test 3: Check MCP protocol compliance
      console.log("3️⃣  Testing MCP protocol compliance...");
      const protocolTest = await this.testMCPProtocol();
      if (!protocolTest) {
        this.results.errors.push("MCP protocol compliance failed");
        return false;
      }
      console.log("✅ MCP protocol compliance successful\n");

      this.results.validationPassed = true;
      return true;
    } catch (error) {
      console.error(`❌ Validation failed: ${error.message}`);
      this.results.errors.push(error.message);
      return false;
    }
  }

  /**
   * Test server startup
   */
  async testServerStartup() {
    return new Promise((resolve) => {
      const serverProcess = spawn("node", ["dist/index.js"], {
        stdio: ["pipe", "pipe", "pipe"],
        env: { ...process.env, DEBUG: "false" },
      });

      let resolved = false;
      const timeout = setTimeout(() => {
        if (!resolved) {
          resolved = true;
          serverProcess.kill();
          resolve(false);
        }
      }, 5000);

      serverProcess.on("spawn", () => {
        if (!resolved) {
          resolved = true;
          clearTimeout(timeout);
          serverProcess.kill();
          resolve(true);
        }
      });

      serverProcess.on("error", () => {
        if (!resolved) {
          resolved = true;
          clearTimeout(timeout);
          resolve(false);
        }
      });
    });
  }

  /**
   * Validate tool registration by analyzing source files
   */
  async validateToolRegistration() {
    try {
      const toolFiles = [
        "src/tools/project-tools.ts",
        "src/tools/file-tools.ts",
        "src/tools/build-tools.ts",
        "src/tools/package-tools.ts",
        "src/tools/simulator-tools.ts",
        "src/tools/xcode-tools.ts",
        "src/tools/development-tools.ts",
        "src/tools/context-aware-tools.ts",
        "src/tools/code-analysis-tools.ts",
      ];

      let totalTools = 0;
      const categories = new Set();

      for (const file of toolFiles) {
        try {
          const content = await fs.readFile(file, "utf-8");

          // Count tool registrations
          const toolMatches = content.match(/server\.server\.tool\(/g) || [];
          totalTools += toolMatches.length;

          // Extract category from file name
          const category = file
            .split("/")
            .pop()
            .replace("-tools.ts", "")
            .replace("-", " ");
          categories.add(category);

          console.log(`   📁 ${file}: ${toolMatches.length} tools`);
        } catch (error) {
          console.log(`   ⚠️  Could not read ${file}: ${error.message}`);
          this.results.warnings.push(`Could not read ${file}`);
        }
      }

      this.results.toolsFound = totalTools;
      this.results.categoriesFound = categories.size;

      console.log(`   📊 Total tools found: ${totalTools}`);
      console.log(`   📂 Categories found: ${categories.size}`);

      if (totalTools >= 70) {
        console.log("   ✅ Tool count meets requirements (70+)");
        return true;
      } else {
        console.log(`   ⚠️  Tool count below expected (${totalTools}/70+)`);
        this.results.warnings.push(
          `Tool count below expected: ${totalTools}/70+`
        );
        return totalTools >= 60; // Allow some flexibility
      }
    } catch (error) {
      console.error(
        `   ❌ Tool registration validation failed: ${error.message}`
      );
      return false;
    }
  }

  /**
   * Test MCP protocol compliance
   */
  async testMCPProtocol() {
    // For now, we'll do a simplified test - check if the server can start and stop cleanly
    // A full MCP protocol test would require a more complex setup
    try {
      const serverProcess = spawn("node", ["dist/index.js"], {
        stdio: ["pipe", "pipe", "pipe"],
        env: { ...process.env, DEBUG: "false" },
      });

      // Give the server a moment to start
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Check if process is still running (not crashed)
      if (serverProcess.killed || serverProcess.exitCode !== null) {
        console.log("   ❌ Server crashed during startup");
        return false;
      }

      // Kill the server
      serverProcess.kill();

      // Wait for clean shutdown
      await new Promise((resolve) => setTimeout(resolve, 1000));

      console.log("   ✅ MCP protocol basic compliance test passed");
      return true;
    } catch (error) {
      console.log(`   ❌ MCP protocol test failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Print validation results
   */
  printResults() {
    console.log("=".repeat(60));
    console.log("📊 MCP SERVER VALIDATION RESULTS");
    console.log("=".repeat(60));

    console.log(`🔧 Tools Found: ${this.results.toolsFound}`);
    console.log(`📂 Categories: ${this.results.categoriesFound}`);
    console.log(
      `✅ Validation Passed: ${this.results.validationPassed ? "YES" : "NO"}`
    );

    if (this.results.errors.length > 0) {
      console.log("\n❌ ERRORS:");
      this.results.errors.forEach((error) => console.log(`  - ${error}`));
    }

    if (this.results.warnings.length > 0) {
      console.log("\n⚠️  WARNINGS:");
      this.results.warnings.forEach((warning) => console.log(`  - ${warning}`));
    }

    console.log("\n" + "=".repeat(60));

    if (this.results.validationPassed) {
      console.log("🎉 MCP SERVER VALIDATION SUCCESSFUL!");
      console.log("✅ Server is ready for production deployment.");
    } else {
      console.log("❌ MCP SERVER VALIDATION FAILED!");
      console.log("⚠️  Please address the errors above before deployment.");
    }

    console.log("=".repeat(60));
  }

  /**
   * Run complete validation
   */
  async run() {
    console.log("🧪 MCP Server Validation Suite\n");

    const success = await this.validateServer();
    this.printResults();

    process.exit(success ? 0 : 1);
  }
}

// Run validation if script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const validator = new MCPValidator();
  validator.run().catch(console.error);
}

export { MCPValidator };
