# Xcode MCP Server

An enterprise-grade MCP (Model Context Protocol) server providing comprehensive Xcode integration for AI assistants. This server enables AI agents to interact with Xcode projects, manage iOS simulators, and perform various Xcode-related tasks with enhanced security, performance monitoring, and robust error handling.

## 🚀 Key Features

- **87 Professional Tools**: Consolidated across 9 categories (project, file, build, package, simulator, xcode, development, context-aware, code-analysis)
- **Enterprise Architecture**: Service-oriented design with dependency injection and intelligent caching
- **60-80% Performance Improvement**: Through advanced caching and parallel processing
- **Professional CLI**: Modern interface with progress indicators and structured status reporting
- **Security First**: Command injection prevention, path validation, input sanitization
- **Production Ready**: Comprehensive error handling, monitoring, and backward compatibility

## 📖 Complete Documentation

This README provides a comprehensive guide including setup instructions, IDE integration, and complete feature documentation. All information has been consolidated into this single document for easy reference.

## 🚀 Quick Start

### Installation

```bash
npm install -g xcode-mcp-server
```

### Basic Usage

1. **Start the server:**

```bash
xcode-server
```

2. **Connect your AI agent** using the MCP protocol via stdio

3. **Use tools** through your AI agent to interact with Xcode projects

## 🔧 IDE Integration

### Cursor IDE Integration

1. **Open Cursor Settings** (Cmd/Ctrl + ,)
2. **Navigate to Extensions** → **MCP Servers**
3. **Add new server configuration:**

```json
{
  "mcpServers": {
    "xcode": {
      "command": "npx",
      "args": ["xcode-mcp-server"],
      "env": {
        "PROJECTS_BASE_DIR": "/Users/<USER>/Developer"
      }
    }
  }
}
```

4. **Restart Cursor** to activate the integration
5. **Start using Xcode tools** in your AI conversations

### Windsurf IDE Integration

1. **Open Windsurf Settings**
2. **Go to MCP Configuration**
3. **Add server configuration:**

```json
{
  "servers": {
    "xcode-mcp-server": {
      "command": "npx",
      "args": ["xcode-mcp-server"],
      "env": {
        "PROJECTS_BASE_DIR": "/Users/<USER>/Developer"
      }
    }
  }
}
```

4. **Save configuration** and restart Windsurf
5. **Access Xcode tools** through the AI assistant

### Claude Desktop Integration

Edit or create `~/Library/Application Support/Claude/claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "xcode": {
      "command": "npx",
      "args": ["xcode-mcp-server"],
      "env": {
        "PROJECTS_BASE_DIR": "/Users/<USER>/Developer"
      }
    }
  }
}
```

### Environment Variables

- `PROJECTS_BASE_DIR`: Base directory for your Xcode projects (default: ~/Developer)
- `DEBUG`: Enable debug logging (set to "true")
- `CACHE_ENABLED`: Enable caching (default: true)

## Features

### Project Management

- Set active projects and get detailed project information
- Create new Xcode projects from templates (iOS, macOS, watchOS, tvOS)
- Add files to Xcode projects with target and group specification
- Parse workspace documents to find associated projects
- List available schemes in projects and workspaces

### File Operations

- Read/write files with support for different encodings
- Handle binary files with base64 encoding/decoding
- Search for text content within files using patterns and regex
- Check file existence and get file metadata
- Create directory structures automatically

### Build & Testing

- Build projects with customizable options
- Run tests with detailed failure reporting
- Analyze code for potential issues
- Clean build directories
- Archive projects for distribution

### CocoaPods Integration

- Initialize CocoaPods in projects
- Install and update pods
- Add and remove pod dependencies
- Execute arbitrary pod commands

### Swift Package Manager

- Initialize new Swift packages
- Add and remove package dependencies with various version requirements
- Update packages and resolve dependencies
- Generate documentation for Swift packages using DocC
- Run tests and build Swift packages

### iOS Simulator Tools

- List available simulators with detailed information
- Boot and shut down simulators
- Install and launch apps on simulators
- Take screenshots and record videos
- Manage simulator settings and state

### Xcode Utilities

- Execute Xcode commands via xcrun
- Compile asset catalogs
- Generate app icon sets from source images
- Trace app performance
- Export and validate archives for App Store submission
- Switch between different Xcode versions

## 🛠️ Complete Tools Reference

### Project Management Tools (11 tools)

- `set_projects_base_dir` - Set base directory for projects
- `set_project_path` - Set active project path
- `get_active_project` - Get current project information
- `find_projects` - Find Xcode projects in directory
- `detect_active_project` - Auto-detect active project
- `change_directory` - Change working directory
- `push_directory` - Push directory to stack
- `pop_directory` - Pop directory from stack
- `get_current_directory` - Get current directory
- `add_file_to_project` - Add files to Xcode project
- `create_workspace` - Create new Xcode workspace

### File Operations Tools (12 tools)

- `read_file` - Read file content with encoding support
- `write_file` - Write/update file content
- `copy_file` - Copy files and directories
- `move_file` - Move files and directories
- `delete_file` - Delete files and directories
- `create_directory` - Create directory structures
- `list_directory` - List directory contents
- `get_file_info` - Get file metadata
- `find_files` - Search for files with patterns
- `resolve_path` - Resolve and validate paths
- `check_file_exists` - Check file existence
- `search_in_files` - Search text within files

### Build & Test Tools (7 tools)

- `build_project` - Build Xcode projects
- `run_tests` - Execute test suites
- `clean_project` - Clean build artifacts
- `archive_project` - Archive for distribution
- `list_available_destinations` - List build destinations
- `list_available_schemes` - List project schemes
- `export_archive` - Export archives for distribution

### Package Management Tools (15 tools)

**CocoaPods (9 tools):**

- `pod_install` - Install CocoaPods dependencies
- `pod_update` - Update pod dependencies
- `pod_outdated` - Check for outdated pods
- `pod_repo_update` - Update spec repositories
- `pod_deintegrate` - Remove CocoaPods integration
- `check_cocoapods` - Check CocoaPods status
- `pod_init` - Initialize Podfile

**Swift Package Manager (6 tools):**

- `init_swift_package` - Initialize SPM project
- `add_swift_package` - Add package dependency
- `remove_swift_package` - Remove package dependency
- `build_swift_package` - Build SPM project
- `test_swift_package` - Test SPM project
- `update_swift_package` - Update dependencies
- `show_swift_dependencies` - Show dependency tree
- `clean_swift_package` - Clean SPM artifacts

### Simulator Control Tools (11 tools)

- `list_booted_simulators` - List running simulators
- `list_simulators` - List all available simulators
- `boot_simulator` - Start simulator
- `shutdown_simulator` - Stop simulator
- `install_app` - Install app on simulator
- `launch_app` - Launch app on simulator
- `terminate_app` - Terminate running app
- `open_url` - Open URL in simulator
- `take_screenshot` - Capture simulator screenshot
- `reset_simulator` - Reset simulator state
- `list_installed_apps` - List installed apps

### Xcode Integration Tools (8 tools)

- `run_xcrun` - Execute Xcode tools via xcrun
- `compile_asset_catalog` - Compile .xcassets
- `run_lldb` - Launch LLDB debugger
- `trace_app` - Performance tracing with xctrace
- `get_xcode_info` - Get Xcode installation info
- `switch_xcode` - Switch active Xcode version
- `validate_app` - Validate app for App Store
- `generate_icon_set` - Generate app icon sets

### Development Tools (6 tools)

- `list_project_files` - List all project files
- `analyze_file` - Analyze source files for issues
- `get_project_configuration` - Get project settings
- `debug_project_info` - Comprehensive project debugging
- `analyze_code_quality` - Code quality analysis
- `scan_for_errors` - Scan for compilation errors

### Context-Aware Tools (11 tools)

- `analyze_project_structure` - Analyze project architecture
- `get_project_health` - Get project health metrics
- `monitor_file_changes` - Monitor file system changes
- `get_performance_metrics` - Get performance statistics
- `search_symbols_advanced` - Advanced symbol search
- `detect_architectural_patterns` - Detect code patterns
- `get_swiftui_components` - Analyze SwiftUI components
- `analyze_combine_usage` - Analyze Combine framework usage
- `index_project_symbols` - Index project symbols
- `query_code_symbols` - Query indexed symbols
- `analyze_code_dependencies` - Analyze code dependencies

### Code Analysis Tools (6 tools)

- `analyze_code_quality` - Comprehensive code quality analysis
- `scan_for_errors` - Scan for compilation errors
- `get_error_report` - Get detailed error reports
- `resolve_error` - Mark errors as resolved
- `get_code_metrics` - Get code quality metrics
- `analyze_project_errors` - Analyze project-wide errors

## Installation

### Prerequisites

- macOS with Xcode 14.0 or higher installed
- Node.js 16 or higher
- npm or yarn
- Swift 5.5+ for Swift Package Manager features
- CocoaPods (optional, for CocoaPods integration)

### Setup

#### Option 1: Automated Setup (Recommended)

Use the included setup script which automates the installation and configuration process:

```bash
# Make the script executable
chmod +x setup.sh

# Run the setup script
./setup.sh
```

**What the Setup Script Does:**

1. **Environment Verification**:

   - Checks that you're running on macOS
   - Verifies Xcode is installed and accessible
   - Confirms Node.js (v16+) and npm are available
   - Checks for Ruby installation
   - Verifies CocoaPods installation (offers to install if missing)

2. **Dependency Installation**:

   - Runs `npm install` to install all required Node.js packages
   - Executes `npm run build` to compile the TypeScript code

3. **Configuration Setup**:

   - Creates a `.env` file if one doesn't exist
   - Prompts for your projects base directory
   - Asks if you want to enable debug logging
   - Saves your configuration preferences

4. **Claude Desktop Integration** (Optional):
   - Offers to configure the server for Claude Desktop
   - Creates or updates the Claude Desktop configuration file
   - Sets up the proper command and arguments to launch the server

**When to Use the Setup Script:**

- First-time installation to ensure all prerequisites are met
- When you want guided configuration with interactive prompts
- If you want to quickly set up Claude Desktop integration
- To verify your environment has all necessary components

The script will guide you through the configuration process with clear prompts and helpful feedback.

#### Option 2: Manual Setup

**When to Use Manual Setup:**

- You prefer explicit control over each installation step
- You have a custom environment or non-standard configuration
- You're setting up in a CI/CD pipeline or automated environment
- You want to customize specific aspects of the installation process
- You're an experienced developer familiar with Node.js projects

Follow these steps for manual installation:

1. Clone the repository:

   ```bash
   git clone https://github.com/r-huijts/xcode-mcp-server.git
   cd xcode-mcp-server
   ```

2. Verify prerequisites (these must be installed):

   - Xcode and Xcode Command Line Tools
   - Node.js v16 or higher
   - npm
   - Ruby (for CocoaPods support)
   - CocoaPods (optional, for pod-related features)

3. Install dependencies:

   ```bash
   npm install
   ```

4. Build the project:

   ```bash
   npm run build
   ```

5. Create a configuration file:

   ```bash
   # Option A: Start with the example configuration
   cp .env.example .env

   # Option B: Create a minimal configuration
   echo "PROJECTS_BASE_DIR=/path/to/your/projects" > .env
   echo "DEBUG=false" >> .env
   ```

   Edit the `.env` file to set your preferred configuration.

6. For Claude Desktop integration (optional):
   - Edit or create `~/Library/Application Support/Claude/claude_desktop_config.json`
   - Add the following configuration (adjust paths as needed):
   ```json
   {
     "mcpServers": {
       "xcode": {
         "command": "node",
         "args": ["/path/to/xcode-mcp-server/dist/index.js"]
       }
     }
   }
   ```

### Setup Troubleshooting

**Common Setup Issues:**

1. **Build Errors**:

   - Ensure you have the correct Node.js version (v16+)
   - Try deleting `node_modules` and running `npm install` again
   - Check for TypeScript errors with `npx tsc --noEmit`
   - Make sure all imports in the code are properly resolved

2. **Missing Dependencies**:

   - If you see errors about missing modules, run `npm install` again
   - For native dependencies, you may need Xcode Command Line Tools: `xcode-select --install`

3. **Permission Issues**:

   - Ensure you have write permissions to the installation directory
   - For CocoaPods installation, you may need to use `sudo gem install cocoapods`

4. **Configuration Problems**:

   - Verify your `.env` file has the correct format and valid paths
   - Make sure `PROJECTS_BASE_DIR` points to an existing directory
   - Check that the path doesn't contain special characters that need escaping

5. **Claude Desktop Integration**:
   - Ensure the path in the Claude configuration points to the correct location of `index.js`
   - Restart Claude Desktop after making configuration changes
   - Check that the server is running before attempting to use it with Claude

## Usage

### Starting the Server

```bash
npm start
```

For development mode with automatic restarts:

```bash
npm run dev
```

### Configuration Options

You can configure the server in two ways:

1. Environment variables in `.env` file:

   ```
   PROJECTS_BASE_DIR=/path/to/your/projects
   DEBUG=true
   ALLOWED_PATHS=/path/to/additional/allowed/directory
   PORT=8080
   ```

2. Command line arguments:
   ```bash
   npm start -- --projects-dir=/path/to/your/projects --port=8080
   ```

### Key Configuration Parameters

- `PROJECTS_BASE_DIR` / `--projects-dir`: Base directory for projects (required)
- `ALLOWED_PATHS` / `--allowed-paths`: Additional directories to allow access to (comma-separated)
- `PORT` / `--port`: Port to run the server on (default: 3000)
- `DEBUG` / `--debug`: Enable debug logging (default: false)
- `LOG_LEVEL` / `--log-level`: Set logging level (default: info)

### Connecting to AI Assistants

The server implements the Model Context Protocol (MCP), making it compatible with various AI assistants that support this protocol. To connect:

1. Start the Xcode MCP server
2. Configure your AI assistant to use the server URL (typically `http://localhost:3000`)
3. The AI assistant will now have access to all the Xcode tools provided by the server

### Tool Documentation

For a comprehensive overview of all available tools and their usage, see [Tools Overview](docs/tools-overview.md).

For detailed usage examples and best practices, see [User Guide](docs/user-guide.md).

### Common Workflows

#### Setting Up a New Project

```javascript
// Create a new iOS app project
await tools.create_xcode_project({
  name: "MyAwesomeApp",
  template: "ios-app",
  outputDirectory: "~/Projects",
  organizationName: "My Organization",
  organizationIdentifier: "com.myorganization",
  language: "swift",
  includeTests: true,
  setAsActive: true,
});

// Add a Swift Package dependency
await tools.add_swift_package({
  url: "https://github.com/Alamofire/Alamofire.git",
  version: "from: 5.0.0",
});
```

#### Working with Files

```javascript
// Read a file with specific encoding
const fileContent = await tools.read_file({
  filePath: "MyAwesomeApp/AppDelegate.swift",
  encoding: "utf-8",
});

// Write to a file
await tools.write_file({
  path: "MyAwesomeApp/NewFile.swift",
  content: "import Foundation\n\nclass NewClass {}\n",
  createIfMissing: true,
});

// Search for text in files
const searchResults = await tools.search_in_files({
  directory: "MyAwesomeApp",
  pattern: "*.swift",
  searchText: "class",
  isRegex: false,
});
```

#### Building and Testing

```javascript
// Build the project
await tools.build_project({
  scheme: "MyAwesomeApp",
  configuration: "Debug",
});

// Run tests
await tools.test_project({
  scheme: "MyAwesomeApp",
  testPlan: "MyAwesomeAppTests",
});
```

## Project Structure

```
xcode-mcp-server/
├── src/
│   ├── index.ts                 # Entry point with professional CLI
│   ├── server.ts                # MCP server implementation with DI
│   ├── types/                   # TypeScript type definitions
│   │   └── index.ts             # Core type definitions
│   ├── services/                # Enterprise service layer
│   │   ├── service-container.ts # Dependency injection container
│   │   ├── command-service.ts   # Secure command execution
│   │   ├── cache-service.ts     # Intelligent caching system
│   │   ├── path-service.ts      # Path validation and management
│   │   └── file-service.ts      # File operation services
│   ├── tools/                   # Consolidated tool implementations (70+ tools)
│   │   ├── base/                # Base classes and infrastructure
│   │   │   └── tool-base.ts     # ToolBase class for consistency
│   │   ├── project-tools.ts     # Project management (12 tools)
│   │   ├── file-tools.ts        # File operations (13 tools)
│   │   ├── build-tools.ts       # Build and testing (7 tools)
│   │   ├── development-tools.ts # Development utilities (4 tools)
│   │   ├── package-tools.ts     # Package management (15 tools)
│   │   ├── simulator-tools.ts   # iOS simulator control (11 tools)
│   │   └── xcode-tools.ts       # Xcode utilities (8 tools)
│   └── utils/                   # Utility functions and helpers
│       ├── core/                # Core utilities
│       │   ├── errors.ts        # Error handling classes
│       │   └── stringUtilities.ts # String manipulation utilities
│       ├── project/             # Project management utilities
│       │   ├── projectManager.ts # Project discovery and management
│       │   ├── projectDirectoryState.ts # Directory state management
│       │   └── xcodeProjectAnalyzer.ts # Project analysis utilities
│       ├── services/            # Service utilities
│       │   ├── healthMonitor.ts # Health monitoring
│       │   ├── performanceMonitor.ts # Performance tracking
│       │   └── securityManager.ts # Security utilities
│       └── testing/             # Testing utilities
│           └── testFramework.ts # Test framework utilities
├── scripts/                     # Build and validation scripts
│   └── validate-consolidation.js # Production readiness validation
├── docs/                        # Documentation
│   └── COMPREHENSIVE_GUIDE.md   # Complete usage guide
└── dist/                        # Compiled code (generated)
```

## 🏗️ Architecture & Security

The Xcode MCP server uses a modern, enterprise-grade architecture with multiple layers of security and performance optimizations:

### 🔒 Security Features

- **Command Injection Prevention**: All external commands use parameter arrays instead of string concatenation
- **Path Validation**: Strict boundary checking prevents access outside allowed directories
- **Input Sanitization**: All user inputs are validated and sanitized before processing
- **Secure File Operations**: Path traversal protection and access control validation
- **Error Context Isolation**: Sensitive information is not leaked in error messages

### ⚡ Performance Optimizations

- **Intelligent Caching**: Time-based cache with LRU eviction for expensive operations
- **Parallel Processing**: Concurrent execution of independent operations
- **Performance Monitoring**: Automatic detection and reporting of slow operations
- **Resource Management**: Proper cleanup and disposal of system resources
- **Retry Mechanisms**: Exponential backoff for transient failures

### 🏗️ Modern Architecture

- **Dependency Injection**: Service container for better testability and modularity
- **Service Lifetime Management**: Singleton, transient, and scoped service patterns
- **Standardized Tool Registration**: Factory pattern for consistent tool implementation
- **Error Handling Pipeline**: Centralized error processing with context enrichment
- **Utility Consolidation**: Shared utilities to eliminate code duplication

## How It Works

The Xcode MCP server uses the Model Context Protocol to provide a standardized interface for AI models to interact with Xcode projects. The server architecture is designed with several key components:

### Core Components

1. **Service Container**: Manages dependency injection and service lifecycle

2. **Secure Command Executor**: Prevents command injection while providing robust command execution

3. **Path Management System**: Ensures secure file access by validating all paths against allowed directories

4. **Performance Monitor**: Tracks operation performance and identifies bottlenecks

5. **Caching Layer**: Reduces redundant operations and improves response times

6. **Project Management**: Detects, loads, and manages different types of Xcode projects:

   - Standard Xcode projects (.xcodeproj)
   - Xcode workspaces (.xcworkspace)
   - Swift Package Manager projects (Package.swift)

7. **Directory State**: Maintains the active directory context for relative path resolution.

8. **Tool Registry**: Organizes tools into logical categories for different Xcode operations.

### Request Flow

1. An AI assistant sends a tool execution request to the MCP server.

2. The server validates the request parameters and permissions.

3. The appropriate tool handler is invoked with the validated parameters.

4. The tool executes the requested operation, often using native Xcode commands.

5. Results are formatted and returned to the AI assistant.

6. Comprehensive error handling provides meaningful feedback for troubleshooting.

### Safety Features

- **Path Validation**: All file operations are restricted to allowed directories.
- **Error Handling**: Detailed error messages help diagnose issues.
- **Parameter Validation**: Input parameters are validated using Zod schemas.
- **Process Management**: External processes are executed safely with proper error handling.

### Project Type Support

The server intelligently handles different project types:

- **Standard Projects**: Direct .xcodeproj manipulation
- **Workspaces**: Manages multiple projects within a workspace
- **SPM Projects**: Handles Swift Package Manager specific operations

This architecture allows AI assistants to seamlessly work with any type of Xcode project while maintaining security and providing detailed feedback.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow the existing code style and organization
- Add comprehensive error handling with specific error messages
- Write tests for new functionality
- Update documentation to reflect your changes
- Ensure compatibility with different project types (standard, workspace, SPM)

### Adding New Tools

To add a new tool to the server:

1. Identify the appropriate category in the `src/tools/` directory
2. Implement the tool using the existing patterns with Zod schema validation
3. Register the tool in the category's `index.ts` file
4. Add error handling with specific error messages
5. Document the tool in the appropriate documentation files

## 🔧 Troubleshooting

### Common Issues

#### 1. Server Won't Start

**Symptoms:** Server fails to start or exits immediately
**Solutions:**

- Verify Xcode Command Line Tools: `xcode-select --install`
- Check Node.js version: `node --version` (requires v16+)
- Ensure proper permissions: `chmod +x dist/index.js`
- Check for port conflicts if using HTTP mode

#### 2. Permission Errors

**Symptoms:** "Permission denied" or "Access denied" errors
**Solutions:**

- Verify file permissions in your projects directory
- Check that the user has read/write access to project files
- Ensure the projects base directory is correctly set
- Run with appropriate user permissions (avoid sudo unless necessary)

#### 3. Build Failures

**Symptoms:** Build commands fail or return errors
**Solutions:**

- Verify Xcode command line tools: `xcode-select -p`
- Check project configuration and schemes
- Use `clean_project` tool before building
- Ensure the correct Xcode version is selected
- Verify all dependencies are installed (CocoaPods, SPM)

#### 4. Tool Not Found Errors

**Symptoms:** "Tool not available" or "Command not found"
**Solutions:**

- Restart the MCP server
- Check that all 70 tools are registered: use validation script
- Verify the tool name spelling and parameters
- Check server logs for registration errors

#### 5. Project Detection Issues

**Symptoms:** "No active project" or project not detected
**Solutions:**

- Use `detect_active_project` tool to auto-detect
- Manually set project with `set_project_path`
- Verify the project directory contains .xcodeproj or .xcworkspace
- Check that the projects base directory is correctly configured

### Debug Mode

Enable detailed logging for troubleshooting:

```bash
DEBUG=true xcode-server
```

This provides verbose output including:

- Service initialization details
- Tool registration information
- Command execution logs
- Performance metrics
- Error stack traces

### Getting Help

1. **Check the logs** - Enable debug mode for detailed information
2. **Verify environment** - Ensure all prerequisites are met
3. **Test with simple operations** - Start with basic file operations
4. **Check GitHub issues** - Look for similar problems and solutions

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Thanks to the Model Context Protocol team for the MCP SDK
- Built with TypeScript and Node.js
- Uses Xcode command line tools and Swift Package Manager
- Special thanks to all contributors who have helped improve the server's functionality and robustness
